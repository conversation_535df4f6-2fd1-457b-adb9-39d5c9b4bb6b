<template>
  <div class="dialog-overlay" v-if="visible" @click.self="onCancel">
    <div class="dialog-container">
      <!-- 对话框标题栏 -->
      <div class="dialog-header">
        <h3>分组管理</h3>
        <button class="close-btn" @click="onCancel" title="关闭">&times;</button>
      </div>

      <!-- 对话框内容区域 -->
      <div class="dialog-body">
        <!-- 分组列表 -->
        <div class="group-list">
          <!-- 分组列表标题和添加按钮 -->
          <div class="group-list-header">
            <h4>当前分组</h4>
            <button class="add-btn" @click="openAddGroupDialog" title="添加新分组">
              <i class="icon-add"></i> 添加分组
            </button>
          </div>

          <!-- 分组列表内容 -->
          <div class="group-list-content">
            <!-- 空状态提示 -->
            <div v-if="groups.length === 0" class="empty-state">
              暂无分组
            </div>

            <!-- 分组列表项 -->
            <div v-else class="group-items">
              <!-- 主分类和子分类的层级结构 -->
              <template v-if="enableSubcategories">
                <div v-for="category in hierarchicalGroups" :key="category.fullName" class="group-category">
                  <!-- 主分类 -->
                  <div class="group-item main-category">
                    <div class="group-name">{{ category.name }}</div>
                    <!-- 主分类操作按钮 - 默认分组不可编辑和删除 -->
                    <div class="group-actions" v-if="category.name !== '默认'">
                      <button class="group-action-btn edit-btn" @click="openEditGroupDialog(category.fullName)" title="编辑分组">
                        <i class="group-edit-icon"></i>
                      </button>
                      <button class="group-action-btn delete-btn" @click="confirmDeleteGroup(category.fullName)" title="删除分组">
                        <i class="group-delete-icon"></i>
                      </button>
                    </div>
                  </div>

                  <!-- 子分类 -->
                  <div v-if="category.children && category.children.length > 0" class="subcategories">
                    <div v-for="subCategory in category.children" :key="subCategory.fullName" class="group-item sub-category">
                      <div class="group-name">{{ subCategory.name }}</div>
                      <!-- 子分类操作按钮 -->
                      <div class="group-actions">
                        <button class="group-action-btn edit-btn" @click="openEditGroupDialog(subCategory.fullName)" title="编辑分组">
                          <i class="group-edit-icon"></i>
                        </button>
                        <button class="group-action-btn delete-btn" @click="confirmDeleteGroup(subCategory.fullName)" title="删除分组">
                          <i class="group-delete-icon"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 普通平面列表（未启用子分类时） -->
              <template v-else>
                <div v-for="group in groups" :key="group" class="group-item">
                  <div class="group-name">{{ group }}</div>
                  <!-- 分组操作按钮 - 默认分组不可编辑和删除 -->
                  <div class="group-actions" v-if="group !== '默认'">
                    <button class="group-action-btn edit-btn" @click="openEditGroupDialog(group)" title="编辑分组">
                      <i class="group-edit-icon"></i>
                    </button>
                    <button class="group-action-btn delete-btn" @click="confirmDeleteGroup(group)" title="删除分组">
                      <i class="group-delete-icon"></i>
                    </button>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框底部按钮区域 -->
      <div class="dialog-footer">
        <button class="confirm-btn" @click="onCancel">关闭</button>
      </div>
    </div>

    <!-- 添加/编辑分组对话框 -->
    <SolutionGroupDialog
      :visible="showGroupDialog"
      :edit-group="editingGroup"
      :existing-groups="groups"
      @cancel="closeGroupDialog"
      @confirm="handleGroupDialogConfirm"
    />
  </div>
</template>

<script setup>
/**
 * 分组管理对话框组件
 *
 * 用于管理方案的分组，包括添加、编辑和删除分组。
 * 支持亮色主题和暗色主题。
 */
import { ref, computed, onMounted } from 'vue';
import { useElectronAPI } from '../plugins/electron';
import SolutionGroupDialog from './SolutionGroupDialog.vue';
import alertService from '../plugins/alert';
import { convertToHierarchical } from '../utils/group-utils';

// 组件属性定义
const props = defineProps({
  /**
   * 对话框是否可见
   */
  visible: {
    type: Boolean,
    default: false
  },
  /**
   * 当前方案ID
   */
  solutionId: {
    type: String,
    default: ''
  },
  /**
   * 当前方案的分组列表
   */
  groups: {
    type: Array,
    default: () => []
  }
});

// 组件事件定义
const emit = defineEmits([
  'cancel',           // 取消对话框
  'group-added',      // 添加分组
  'group-updated',    // 更新分组
  'group-deleted'     // 删除分组
]);

// 获取Electron API
const electronAPI = useElectronAPI();

// ===== 状态管理 =====

// 分组对话框状态
const showGroupDialog = ref(false);
const editingGroup = ref(null);

// 是否启用资源子分类
const enableSubcategories = ref(false);

// 获取设置
const fetchSettings = async () => {
  try {
    const settings = await electronAPI.getSettings();
    enableSubcategories.value = settings?.basic?.enableResourceSubcategories || false;
  } catch (error) {
    console.error('获取设置失败:', error);
    enableSubcategories.value = false;
  }
};

// 组件挂载时获取设置
onMounted(fetchSettings);

// 计算属性 - 层级结构的分组列表
const hierarchicalGroups = computed(() => {
  return convertToHierarchical(props.groups);
});

// ===== 方法定义 =====

/**
 * 打开添加分组对话框
 */
const openAddGroupDialog = () => {
  editingGroup.value = null;
  showGroupDialog.value = true;
};

/**
 * 打开编辑分组对话框
 * @param {string} groupName - 要编辑的分组名称
 */
const openEditGroupDialog = (groupName) => {
  editingGroup.value = {
    name: groupName,
    description: '' // 目前没有分组描述，可以后续添加
  };
  showGroupDialog.value = true;
};

/**
 * 关闭分组对话框
 */
const closeGroupDialog = () => {
  showGroupDialog.value = false;
  editingGroup.value = null;
};

/**
 * 处理分组对话框确认
 * @param {Object} groupData - 分组数据
 */
const handleGroupDialogConfirm = async (groupData) => {
  if (editingGroup.value) {
    // 编辑现有分组
    emit('group-updated', {
      oldName: editingGroup.value.name,
      newName: groupData.name,
      description: groupData.description
    });
  } else {
    // 添加新分组
    emit('group-added', {
      name: groupData.name,
      description: groupData.description
    });
  }

  // 关闭对话框
  closeGroupDialog();
};

/**
 * 确认删除分组
 * @param {string} groupName - 要删除的分组名称
 */
const confirmDeleteGroup = async (groupName) => {
  try {
    // 检查是否为子分类
    const isSubCat = groupName.includes('/');

    // 使用标准确认对话框
    const result = await alertService.confirm({
      title: `删除${isSubCat ? '子分类' : '分组'}`,
      message: `确定要删除${isSubCat ? '子分类' : '分组'} "${groupName}" 吗？此操作不可撤销。`,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'danger'
    });

    if (result) {
      // 默认启用删除资源选项
      emit('group-deleted', {
        name: groupName,
        deleteResources: true
      });
    }
  } catch (err) {
    console.error('确认删除分组失败', err);
  }
};

/**
 * 取消按钮点击事件
 */
const onCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
/**
 * 对话框基础样式
 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-container {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 500px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color 0.2s;
}

.close-btn:hover {
  color: var(--color-text-primary);
}

/* 对话框内容区域 */
.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
}

/* 对话框底部 */
.dialog-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.confirm-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 500;
  background-color: var(--color-primary);
  color: white;
  border: none;
  transition: background-color 0.2s;
}

.confirm-btn:hover {
  background-color: var(--color-primary-dark);
}

/**
 * 分组列表样式
 */
.group-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 分组列表头部 */
.group-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-list-header h4 {
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-primary);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.add-btn:hover {
  background-color: var(--color-primary-dark);
}

/* 分组列表内容区域 */
.group-list-content {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  max-height: 300px;
  overflow-y: auto;
}

/* 空状态提示 */
.empty-state {
  padding: var(--spacing-md);
  text-align: center;
  color: var(--color-text-secondary);
}

/* 分组列表项 */
.group-items {
  display: flex;
  flex-direction: column;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  transition: background-color 0.2s;
}

.group-item:hover {
  background-color: var(--color-background-light);
}

.group-item:last-child {
  border-bottom: none;
}

.group-name {
  font-size: 14px;
  color: var(--color-text-primary);
}

/* 分组操作按钮 */
.group-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.group-action-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  transition: all 0.2s;
}

.group-action-btn:hover {
  background-color: var(--color-background-light);
}

.group-action-btn.edit-btn:hover {
  color: var(--color-primary);
}

.group-action-btn.delete-btn:hover {
  color: var(--color-danger);
}

/**
 * 图标样式
 */
/* 编辑图标 */
.group-edit-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_edit.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  filter: brightness(0) invert(0.2); /* 亮色主题下的图标颜色 */
}

/* 删除图标 */
.group-delete-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_delete.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  filter: brightness(0) invert(0.2); /* 亮色主题下的图标颜色 */
}

/* 添加图标 */
.icon-add {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_add.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  filter: brightness(0) invert(1); /* 白色图标，适合在彩色按钮上使用 */
}

/**
 * 主题适配
 */
/* 深色主题下的图标样式 */
[data-theme="dark"] .group-edit-icon,
[data-theme="dark"] .group-delete-icon {
  filter: brightness(0) invert(1); /* 深色主题下的图标颜色 */
}

/* 深色主题下的分组项悬停效果 */
[data-theme="dark"] .group-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 深色主题下的操作按钮悬停效果 */
[data-theme="dark"] .group-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/**
 * 层级结构样式
 */
.group-category {
  display: flex;
  flex-direction: column;
}

.main-category {
  font-weight: 500;
  background-color: var(--color-background-light);
}

.subcategories {
  margin-left: var(--spacing-md);
  border-left: 2px solid var(--color-border-light);
}

.sub-category {
  padding-left: var(--spacing-lg);
  position: relative;
}

.sub-category::before {
  content: '';
  position: absolute;
  left: calc(var(--spacing-md) - 1px);
  top: 50%;
  width: var(--spacing-md);
  height: 1px;
  background-color: var(--color-border-light);
}

/* 深色主题下的层级结构样式 */
[data-theme="dark"] .main-category {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .subcategories {
  border-left-color: var(--color-border);
}

[data-theme="dark"] .sub-category::before {
  background-color: var(--color-border);
}
</style>
