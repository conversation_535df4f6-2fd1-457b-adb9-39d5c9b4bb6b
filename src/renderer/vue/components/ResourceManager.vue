<template>
  <div class="resource-manager-outer">
    <div class="resource-manager">
      <!-- 左侧：分组与分类导航 -->
      <div class="group-navigation">
        <!-- 方案logo区域 -->
        <!-- <div class="solution-logo" @click="showLogoUpload">
          <img v-if="logoPreview" :src="logoPreview" alt="方案logo" />
          <div v-else class="img-bg-placeholder" aria-label="无缩略图"></div>
          <div class="logo-overlay">
            <i class="icon-edit"></i>
          </div>
        </div> -->

        <!-- 方案logo区域 -->
        <div
          class="solution-logo"
          :class="{ empty: !logoPreview }"
          @click="logoDialog = true"
        >
          <div
            v-if="logoInRequest"
            class="img-bg-placeholder"
            aria-label="无缩略图"
          ></div>
          <div v-if="logoPreview" class="logo-img">
            <img :src="logoPreview" alt="方案logo" />
          </div>
          <div v-else class="empty-logo">
            <img src="@assets/svg/add_resource.svg" alt="" />
            <div>上传LOGO</div>
          </div>

          <div class="logo-overlay">
            <img src="@assets/svg/solution_edit.svg" alt="" />
          </div>
        </div>

        <!-- 编辑分组名称的对话框 -->
        <el-dialog
          v-model="logoDialog"
          :title="logoPreview ? '更新LOGO' : '上传LOGO'"
          width="30%"
          :center="true"
          :close-on-click-modal="false"
          destroy-on-close
          class="logo-dialog"
        >
          <div class="upload-logo" @click="showLogoUpload">
            <!-- 显示预览图或默认图标 -->
            <!-- <img v-if="previewUrl" :src="previewUrl" class="logo-preview" /> -->
            <img src="@assets/svg/add_logo.svg" alt="添加LOGO" />
          </div>

          <div class="logo-tip">请上传 322px x 126 px，png/jpg 格式LOGO</div>

          <!-- <div v-if="logoPreview" class="logo-tip">文件名：{{ logoPreview }}</div> -->

          <!-- <template #footer>
            <span class="dialog-footer">
              <el-button
                type="primary"
                class="confirm-btn logo-confirm-btn"
                @click="submitUpload"
                >上传</el-button
              >
            </span>
          </template> -->
        </el-dialog>

        <!-- 添加分组按钮 -->
        <div class="add-group-content" @click="showAddGroupDialog">
          <img src="@assets/svg/group_add.svg" alt="" />
          <span v-if="!isAddingGroup">添加新分类</span>
        </div>

        <!-- 使用Element Plus的Tree组件替换原有的分组树 -->
        <div class="group-tree">
          <el-tree
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            :current-key="selectedNodeKey"
            @node-click="handleNodeClick"
            @node-contextmenu="handleNodeContextMenu"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span class="node-content">
                  <span
                    class="node-label"
                    :class="{ 'is-current': selectedGroup === data.fullName }"
                  >
                    {{ data.name }}
                    <span
                      class="group-count"
                      v-if="groupResourceCounts[data.fullName]"
                    >
                      ({{ groupResourceCounts[data.fullName] }})
                    </span>
                  </span>
                </span>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 右键菜单 -->
        <div
          v-if="contextMenuVisible"
          class="context-menu"
          :style="contextMenuStyle"
        >
          <div class="context-menu-item" @click="handleContextMenuEdit">
            <span>编辑名称</span>
          </div>
          <div class="context-menu-item" @click="handleContextMenuDelete">
            <span>删除分类</span>
          </div>
          <div
            v-if="!contextMenuData.isSubcategory"
            class="context-menu-item"
            @click="handleContextMenuAddSubcategory"
          >
            <span>添加子分类</span>
          </div>
        </div>

        <!-- 编辑分组名称的对话框 -->
        <el-dialog
          v-model="editDialogVisible"
          :title="isEditingSubcategory ? '编辑子分类' : '编辑分组'"
          width="30%"
          :center="true"
          :close-on-click-modal="false"
          destroy-on-close
        >
          <el-form :model="editForm" label-width="80px" @submit.prevent>
            <el-form-item
              label="名称"
              :rules="[
                { required: true, message: '请输入名称', trigger: 'blur' },
              ]"
            >
              <el-input v-model="editForm.name" placeholder="请输入名称" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button
                type="primary"
                class="confirm-btn"
                @click="handleEditConfirm"
                >确定</el-button
              >
            </span>
          </template>
        </el-dialog>

        <!-- 添加分组/子分类的对话框 -->
        <el-dialog
          v-model="addDialogVisible"
          :title="addingSubcategoryParent ? '添加子分类' : '添加新分类'"
          width="30%"
          :close-on-click-modal="false"
          destroy-on-close
          :center="true"
        >
          <el-form :model="addForm" label-width="80px" @submit.prevent>
            <el-form-item
              label="名称"
              :rules="[
                { required: true, message: '请输入名称', trigger: 'blur' },
              ]"
            >
              <el-input v-model="addForm.name" placeholder="请输入名称" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button
                type="primary"
                class="confirm-btn"
                @click="handleAddConfirm"
                >确定</el-button
              >
            </span>
          </template>
        </el-dialog>

        <div class="version-label">版本号：v {{ versionInfo.version }}</div>
      </div>

      <!-- 右侧：资源视图 -->
      <div class="resource-view">
        <div
          class="resource-grid"
          :class="{ empty: filteredResources.length === 0 }"
        >
          <template v-if="filteredResources.length > 0">
            <ResourceCard
              v-for="resource in filteredResources"
              :key="resource.index"
              :resource="resource"
              :selected="
                selectedResource && selectedResource.index === resource.index
              "
              :show-groups="true"
              :controls="['edit', 'delete']"
              :enable-context-menu="true"
              :show-preview="false"
              @click="editResource(resource)"
              @edit="editResource(resource)"
              @delete="confirmDeleteResource(resource)"
              @play-video="playVideo(resource)"
              @preview-image="previewImage(resource)"
            >
              <template #actions>
                <div class="resource-actions">
                  <button class="btn btn-text" @click="handleEdit(resource)">
                    <i class="icon-edit"></i>
                  </button>
                  <button class="btn btn-text" @click="handleDelete(resource)">
                    <i class="icon-delete"></i>
                  </button>
                </div>
              </template>
            </ResourceCard>
          </template>

          <!-- 空状态下直接在网格中显示添加资源卡片 -->
          <div class="add-resource-card" @click="showAddResourceDialog">
            <img src="@assets/svg/add_resource.svg" alt="" />
            <span>添加资源</span>
          </div>
        </div>
      </div>

      <!-- 使用共享的媒体预览对话框组件 -->
      <MediaPreviewDialog
        v-if="showMediaPreview"
        :show="showMediaPreview"
        :type="mediaPreviewType"
        :media="currentMedia"
        :mediaUrl="mediaUrl"
        @close="closeMediaPreview"
        @error="handleMediaError"
      />

      <!-- 新增：右下角设置按钮和菜单 -->
      <div class="settings-btn" @click="showSettingsMenu = !showSettingsMenu">
        <img src="@assets/images/setup.png" alt="" />
      </div>
      <div v-if="showSettingsMenu" class="settings-menu">
        <div class="settings-menu-item" @click="changeVirtualScene">
          更换背景图
        </div>
      </div>

      <!-- 新增：自定义背景图弹窗 -->
      <template v-if="showBgDialog">
        <div class="custom-bg-dialog-mask">
          <div class="custom-bg-dialog">
            <img
              class="custom-bg-dialog-close"
              @click="showBgDialog = false"
              src="@assets/svg/back_dark.svg"
            />
            <div class="custom-bg-dialog-title">更换背景图</div>
            <div class="custom-bg-dialog-content">
              <label class="radio-option">
                <input type="radio" value="default" v-model="bgType" />
                默认背景图
              </label>
              <label class="radio-option">
                <input type="radio" value="custom" v-model="bgType" />
                自定义背景图
                <div class="bottom-text">
                  请上传一张 50 MB 以内，8192px x 4096px 的全景图
                </div>
                <div v-if="bgType === 'custom'" class="custom-bg-upload">
                  <div class="res-name" v-if="customBgFileName">
                    <img src="@assets/svg/resource.svg" />
                    {{ customBgFileName }}
                  </div>
                  <button class="upload-btn" @click.stop="handleBgUpload">
                    {{ customBgFileName ? "重新上传" : "上传" }}
                  </button>
                </div>
              </label>
            </div>

            <div class="custom-bg-dialog-footer">
              <button class="confirm-btn" @click="handleBgConfirm">确定</button>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 编辑分组名称的对话框 -->
    <el-dialog
      v-model="deleteDialog"
      title="删除分类"
      width="30%"
      :center="true"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="dialog-content">
        <div class="dialog-message">
          <p>
            确定要删除{{
              contextMenuData?.isSubcategory ? "子分类" : "分组"
            }}
            "{{ contextMenuData?.name }}" 吗？此操作不可撤销。
          </p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="primary"
            class="confirm-btn"
            @click="confirmDeleteGroup(contextMenuData)"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getMainFileType,
  MainFileType,
  MainTypeLabels,
  TypeLabels,
} from "../../../shared/types/resource.js";
import { DEFAULT_GROUPS } from "../../../shared/constants/defaults.js";
import ResourceCard from "./ResourceCard.vue";
import MediaPreviewDialog from "./MediaPreviewDialog.vue";
import {
  convertToHierarchical,
  isSubcategory,
  getMainCategory,
  getSubcategory,
  createFullGroupName,
} from "../utils/group-utils";
import alertService from "../plugins/alert";
import { useElectronAPI } from "../plugins/electron";
import defaultLogoIcon from "../../assets/images/yvr_logo.png";
import { Folder, Document, Plus, Edit, Delete } from "@element-plus/icons-vue";
import { fa } from "element-plus/es/locales.mjs";

export default {
  name: "ResourceManager",
  components: {
    ResourceCard,
    MediaPreviewDialog,
    Folder,
    Document,
    Plus,
    Edit,
    Delete,
  },
  directives: {
    // 自定义指令：自动聚焦
    focus: {
      mounted(el) {
        el.focus();
      },
    },
  },
  props: {
    solution: {
      type: Object,
      required: true,
    },
    enableSubcategories: {
      type: Boolean,
      default: false,
    },
    selectedGroup: {
      type: String,
      default: "",
    },
  },
  emits: [
    "update:solution",
    "deploy",
    "export",
    "add-resource",
    "edit-resource",
    "delete-resource",
    "add-group",
    "edit-group",
    "delete-group",
    "select-group",
    "update-logo",
    "back",
  ],
  setup(props, { emit }) {
    // 状态
    const selectedGroup = ref(props.selectedGroup);
    const selectedResource = ref(null);
    const searchQuery = ref("");
    const typeFilter = ref("all");
    const sortOption = ref("name");
    const expandedGroups = ref({});

    // 媒体预览相关状态
    const showMediaPreview = ref(false);
    const mediaPreviewType = ref("image"); // 'image' 或 'video'
    const currentMedia = ref(null);
    const mediaUrl = ref("");

    // 默认为 true，不再从 localStorage 读取
    const deleteResourcesWithGroup = ref(true);

    // 分组编辑状态
    const editingGroupId = ref(null);
    const editingGroupName = ref("");
    const editingGroupOriginalName = ref("");

    // 添加分组状态
    const isAddingGroup = ref(false);
    const newGroupName = ref("");

    // 添加子分类状态
    const isAddingSubcategory = ref(false);
    const addingSubcategoryParent = ref(null);
    const newSubcategoryName = ref("");

    const electronAPI = useElectronAPI();
    const logoPreview = ref("");
    const defaultLogoPath = defaultLogoIcon;

    // 新增弹窗状态
    const showBgDialog = ref(false);
    const bgType = ref("default"); // 'default' 或 'custom'
    const customBgFileName = ref("");

    // 新增标志
    const isSelectingFile = ref(false);

    // 新增的状态
    const editDialogVisible = ref(false);
    const addDialogVisible = ref(false);
    const editForm = ref({ name: "" });
    const addForm = ref({ name: "" });
    const currentEditingNode = ref(null);
    const isEditingSubcategory = ref(false);

    // Tree组件的配置
    const defaultProps = {
      children: "children",
      label: "name",
    };

    // 将hierarchicalGroups转换为Tree组件所需的数据结构
    const treeData = computed(() => {
      return hierarchicalGroups.value.map((group) => ({
        id: group.fullName,
        name: group.name,
        fullName: group.fullName,
        isSubcategory: false,
        children:
          group.children?.map((subCategory) => ({
            id: subCategory.fullName,
            name: subCategory.name,
            fullName: subCategory.fullName,
            isSubcategory: true,
          })) || [],
      }));
    });

    // 获取默认展开的节点keys
    const defaultExpandedKeys = computed(() => {
      return hierarchicalGroups.value.map((group) => group.fullName);
    });

    // 当前选中节点的key
    const selectedNodeKey = computed(() => {
      return selectedGroup.value || "";
    });

    // 处理节点点击
    const handleNodeClick = (data) => {
      selectGroup(data.fullName);
    };

    // 开始编辑分组
    const startEditingGroup = (data) => {
      currentEditingNode.value = data;
      isEditingSubcategory.value = data.isSubcategory;
      editForm.value.name = data.name;
      editDialogVisible.value = true;
    };

    // 处理编辑确认
    const handleEditConfirm = async () => {
      if (!currentEditingNode.value || !editForm.value.name.trim()) {
        return;
      }

      try {
        // 构建新的完整名称
        let newFullName;
        if (isEditingSubcategory.value) {
          const mainCategory = getMainCategory(
            currentEditingNode.value.fullName
          );
          newFullName = createFullGroupName(mainCategory, editForm.value.name);
        } else {
          newFullName = editForm.value.name;
        }

        // 发出编辑事件
        emit("edit-group", {
          oldName: currentEditingNode.value.fullName,
          newName: newFullName,
        });

        editDialogVisible.value = false;
      } catch (error) {
        console.error("编辑分组失败:", error);
      }
    };

    // 显示添加分组对话框
    const showAddGroupDialog = () => {
      addingSubcategoryParent.value = null;
      addForm.value.name = "";
      addDialogVisible.value = true;
    };

    // 显示添加子分类对话框
    const showAddSubcategoryDialog = (parentGroup) => {
      addingSubcategoryParent.value = parentGroup;
      addForm.value.name = "";
      addDialogVisible.value = true;
    };

    // 处理添加确认
    const handleAddConfirm = async () => {
      if (!addForm.value.name.trim()) {
        ElMessage.warning("请输入名称");
        return;
      }

      try {
        if (addingSubcategoryParent.value) {
          // 添加子分类
          const parentName = addingSubcategoryParent.value.fullName;
          const newName = addForm.value.name.trim();
          const fullName = createFullGroupName(parentName, newName);

          // 检查分组名称是否已存在
          if (props.solution.groups.includes(fullName)) {
            ElMessage.warning("该分组名称已存在");
            return;
          }

          emit("add-group", {
            name: fullName,
          });
        } else {
          // 添加主分组
          const newName = addForm.value.name.trim();

          // 检查分组名称是否已存在
          if (props.solution.groups.includes(newName)) {
            ElMessage.warning("该分组名称已存在");
            return;
          }

          emit("add-group", {
            name: newName,
          });
        }

        addDialogVisible.value = false;
        ElMessage.success("添加成功");
      } catch (error) {
        console.error("添加分组失败:", error);
        ElMessage.error("添加分组失败");
      }
    };

    // 确认删除分组
    const confirmDeleteGroup = async (data) => {
      try {
        emit("delete-group", data.fullName, deleteResourcesWithGroup.value);
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除分组失败:", error);
          ElMessage.error("删除分组失败");
        }
      }
    };

    // 计算属性 - 层级结构的分组列表
    const hierarchicalGroups = computed(() => {
      if (!props.solution || !props.solution.groups) {
        return [];
      }
      return convertToHierarchical(props.solution.groups);
    });

    // 计算属性 - 主类型选项
    const mainTypeOptions = computed(() => {
      const options = {};
      for (const type in MainFileType) {
        const mainType = MainFileType[type];
        options[mainType] = MainTypeLabels[mainType];
      }
      return options;
    });

    // 计算属性 - 当前位置标题
    const currentLocationTitle = computed(() => {
      if (!selectedGroup.value) {
        return "全部资源";
      }

      if (props.enableSubcategories && isSubcategory(selectedGroup.value)) {
        const mainCategory = getMainCategory(selectedGroup.value);
        const subCategory = getSubcategory(selectedGroup.value);
        return `${mainCategory} / ${subCategory}`;
      }

      return selectedGroup.value;
    });

    // 计算属性 - 分组资源数量
    const groupResourceCounts = computed(() => {
      if (!props.solution || !props.solution.list) {
        return {};
      }

      const counts = {};

      // 计算每个分组的资源数量
      props.solution.list.forEach((resource) => {
        if (resource.groups && resource.groups.length > 0) {
          resource.groups.forEach((group) => {
            if (!counts[group]) {
              counts[group] = 0;
            }
            counts[group]++;

            // 如果是子分类，也计入主分类
            if (props.enableSubcategories && isSubcategory(group)) {
              const mainCategory = getMainCategory(group);
              if (!counts[mainCategory]) {
                counts[mainCategory] = 0;
              }
              counts[mainCategory]++;
            }
          });
        }
      });

      return counts;
    });

    // 计算属性 - 过滤后的资源
    const filteredResources = computed(() => {
      if (!props.solution || !props.solution.list) {
        return [];
      }

      return props.solution.list
        .filter((resource) => {
          // 分组过滤
          if (selectedGroup.value) {
            if (props.enableSubcategories) {
              // 如果是子分类，直接匹配完整分组名
              if (isSubcategory(selectedGroup.value)) {
                if (
                  !resource.groups ||
                  !resource.groups.includes(selectedGroup.value)
                ) {
                  return false;
                }
              } else {
                // 如果是主分类，匹配所有以该主分类开头的分组
                const mainCategory = selectedGroup.value;
                if (
                  !resource.groups ||
                  !resource.groups.some(
                    (group) =>
                      group === mainCategory ||
                      (isSubcategory(group) &&
                        getMainCategory(group) === mainCategory)
                  )
                ) {
                  return false;
                }
              }
            } else {
              // 未启用子分类时，使用原来的匹配方式
              if (
                !resource.groups ||
                !resource.groups.includes(selectedGroup.value)
              ) {
                return false;
              }
            }
          }

          // 类型过滤
          if (typeFilter.value !== "all") {
            const resourceMainType = getMainFileType(resource.type);
            if (resourceMainType !== typeFilter.value) {
              return false;
            }
          }

          // 搜索过滤
          if (searchQuery.value) {
            const query = searchQuery.value.toLowerCase();
            return (
              (resource.showName || "").toLowerCase().includes(query) ||
              (resource.fileName || "").toLowerCase().includes(query) ||
              (resource.describe || "").toLowerCase().includes(query)
            );
          }

          return true;
        })
        .sort((a, b) => {
          // 首先按组排序
          if (selectedGroup.value) {
            // 如果选择了特定组，该组的资源排在前面
            const aInGroup = a.groups && a.groups.includes(selectedGroup.value);
            const bInGroup = b.groups && b.groups.includes(selectedGroup.value);
            if (aInGroup !== bInGroup) {
              return aInGroup ? -1 : 1;
            }
          } else {
            // 如果没有选择组，按第一个组名排序
            const aGroup = (a.groups && a.groups[0]) || "";
            const bGroup = (b.groups && b.groups[0]) || "";
            if (aGroup !== bGroup) {
              return aGroup.localeCompare(bGroup);
            }
          }

          // 然后按 index 升序排序
          return (a.index || 0) - (b.index || 0);
        });
    });

    // 方法 - 选择分组
    const selectGroup = (group) => {
      selectedGroup.value = group;
      selectedResource.value = null;

      // 如果是空字符串，发出'全部'作为事件参数，以便与SolutionsPage.vue保持一致
      const emitValue = group === "" ? "全部" : group;
      emit("select-group", emitValue);
    };

    // 方法 - 切换分组展开状态
    const toggleGroupExpand = (groupName) => {
      expandedGroups.value[groupName] = !expandedGroups.value[groupName];
      console.log(
        `分组 ${groupName} 展开状态: ${expandedGroups.value[groupName]}`
      );
    };

    // 方法 - 展开或折叠所有分组
    const toggleAllGroups = () => {
      const allExpanded = hierarchicalGroups.value.every(
        (group) => expandedGroups.value[group.fullName]
      );

      hierarchicalGroups.value.forEach((group) => {
        expandedGroups.value[group.fullName] = !allExpanded;
      });

      // 如果是展开所有分组，默认选择"全部资源"
      if (!allExpanded) {
        // 直接设置内部状态
        selectedGroup.value = "";
        selectedResource.value = null;

        // 发出事件，通知父组件
        emit("select-group", "全部");
      }
    };

    // 资源详情相关方法已移除

    // 方法 - 获取资源预览URL
    const getResourcePreviewUrl = (resource) => {
      return resource.thumbnail || resource.poster || "";
    };

    // 方法 - 检查是否为图片类型
    const isImageType = (type) => {
      return getMainFileType(type) === MainFileType.IMAGE;
    };

    // 方法 - 检查是否为视频类型
    const isVideoType = (type) => {
      return getMainFileType(type) === MainFileType.VIDEO;
    };

    // 方法 - 预览媒体（视频或图片）
    const previewMedia = async (resource, type) => {
      try {
        console.log(
          `ResourceManager: 预览${type === "video" ? "视频" : "图片"}被调用，资源:`,
          resource
        );

        // 设置当前媒体和类型
        currentMedia.value = resource;
        mediaPreviewType.value = type;

        // 获取媒体文件路径
        if (resource.path && resource.fileName) {
          const path = resource.path.endsWith("/")
            ? `${resource.path}${resource.fileName}`
            : `${resource.path}/${resource.fileName}`;

          console.log("ResourceManager: 媒体路径:", path);

          try {
            // 使用 Electron API 获取本地文件路径
            if (electronAPI && electronAPI.getResourceFilePath) {
              const filePath = await electronAPI.getResourceFilePath(path);
              console.log("ResourceManager: 获取到媒体路径:", filePath);
              mediaUrl.value = filePath;
            } else if (window.ipcRenderer) {
              const filePath = await window.ipcRenderer.invoke(
                "get-resource-file-path",
                path
              );
              console.log("ResourceManager: 通过IPC获取到媒体路径:", filePath);
              mediaUrl.value = filePath;
            } else {
              // 如果没有 API，尝试直接使用路径
              console.log("ResourceManager: 没有可用的API，直接使用路径");
              mediaUrl.value = path;
            }
          } catch (e) {
            // 如果 API 调用失败，尝试直接使用路径
            console.log("ResourceManager: API调用失败，尝试直接使用路径", e);
            mediaUrl.value = path;
          }

          // 显示媒体预览器
          console.log("ResourceManager: 显示媒体预览器");
          showMediaPreview.value = true;
        } else {
          console.error("ResourceManager: 媒体资源路径不完整");
          alertService.alert({
            title: type === "video" ? "播放失败" : "预览失败",
            message: `无法获取${type === "video" ? "视频" : "图片"}文件路径`,
            type: "error",
          });
        }
      } catch (error) {
        console.error(
          `ResourceManager: 预览${type === "video" ? "视频" : "图片"}失败:`,
          error
        );
        alertService.alert({
          title: type === "video" ? "播放失败" : "预览失败",
          message:
            `无法${type === "video" ? "播放视频" : "预览图片"}: ` +
            (error.message || "未知错误"),
          type: "error",
        });
      }
    };

    // 方法 - 播放视频（调用预览媒体方法）
    const playVideo = (resource) => {
      previewMedia(resource, "video");
    };

    // 方法 - 预览图片（调用预览媒体方法）
    const previewImage = (resource) => {
      previewMedia(resource, "image");
    };

    // 方法 - 关闭媒体预览器
    const closeMediaPreview = () => {
      // 重置状态
      showMediaPreview.value = false;
      currentMedia.value = null;
      mediaUrl.value = "";

      // 在下一个 tick 中聚焦回资源列表
      nextTick(() => {
        // 尝试聚焦回资源列表区域
        const resourceGrid = document.querySelector(".resource-grid");
        if (resourceGrid) {
          resourceGrid.focus();
        }
      });
    };

    // 方法 - 处理媒体加载错误
    const handleMediaError = (event) => {
      console.error(
        `${mediaPreviewType.value === "video" ? "视频" : "图片"}加载失败:`,
        event
      );
      alertService.alert({
        title: `${mediaPreviewType.value === "video" ? "播放" : "预览"}失败`,
        message: `无法加载${mediaPreviewType.value === "video" ? "视频" : "图片"}文件`,
        type: "error",
      });
      closeMediaPreview();
    };

    // 方法 - 获取类型标签
    const getTypeLabel = (type) => {
      return TypeLabels[type] || "未知类型";
    };

    // 方法 - 获取显示的分组名称
    const getDisplayGroupName = (group) => {
      if (props.enableSubcategories && isSubcategory(group)) {
        return getSubcategory(group);
      }
      return group;
    };

    // 方法 - 显示添加资源对话框
    const showAddResourceDialog = () => {
      const initialGroup = selectedGroup.value || null;
      emit("add-resource", { initialGroup });
    };

    // 方法 - 编辑资源
    const editResource = (resource) => {
      emit("edit-resource", resource);
    };

    // 方法 - 删除资源（直接触发事件，不再显示确认对话框）
    const confirmDeleteResource = (resource) => {
      console.log(
        `触发删除资源事件: ${resource.showName}, 路径: ${resource.path}`
      );
      // 直接触发删除事件，确认对话框将在父组件中显示
      emit("delete-resource", resource);
      if (
        selectedResource.value &&
        selectedResource.value.index === resource.index
      ) {
        selectedResource.value = null;
      }
    };

    // 方法 - 保存删除资源状态
    const saveDeleteResourcesState = () => {
      localStorage.setItem(
        "deleteResourcesWithGroup",
        deleteResourcesWithGroup.value
      );
      console.log(`保存删除资源状态: ${deleteResourcesWithGroup.value}`);
    };

    // 方法 - 开始添加分组
    const startAddingGroup = () => {
      isAddingGroup.value = true;
      newGroupName.value = "";

      // 聚焦输入框
      nextTick(() => {
        const inputEl = document.querySelector(".add-group-input");
        if (inputEl) {
          inputEl.focus();
        }
      });
    };

    // 方法 - 保存新分组
    const saveNewGroup = () => {
      if (!newGroupName.value.trim()) {
        cancelAddingGroup();
        return;
      }

      // 发出添加分组事件
      emit("add-group", { name: newGroupName.value.trim() });

      // 清除添加状态
      cancelAddingGroup();
    };

    // 方法 - 取消添加分组
    const cancelAddingGroup = () => {
      isAddingGroup.value = false;
      newGroupName.value = "";
    };

    // 方法 - 开始添加子分类
    const startAddingSubcategory = (group) => {
      isAddingSubcategory.value = true;
      addingSubcategoryParent.value = group.fullName;
      newSubcategoryName.value = "";

      // 确保分组是展开的
      expandedGroups.value[group.fullName] = true;

      // 聚焦输入框
      nextTick(() => {
        const inputEl = document.querySelector(".add-subcategory-input");
        if (inputEl) {
          inputEl.focus();
        }
      });
    };

    // 方法 - 保存新子分类
    const saveNewSubcategory = () => {
      if (!newSubcategoryName.value.trim() || !addingSubcategoryParent.value) {
        cancelAddingSubcategory();
        return;
      }

      // 发出添加子分类事件
      emit("add-group", {
        name: newSubcategoryName.value.trim(),
        parentGroup: addingSubcategoryParent.value,
      });

      // 清除添加状态
      cancelAddingSubcategory();
    };

    // 方法 - 取消添加子分类
    const cancelAddingSubcategory = () => {
      isAddingSubcategory.value = false;
      addingSubcategoryParent.value = null;
      newSubcategoryName.value = "";
    };

    // 监听 props.selectedGroup 的变化
    watch(
      () => props.selectedGroup,
      (newValue) => {
        // 如果新值是 '全部'，则将内部状态设置为空字符串
        if (newValue === "全部") {
          selectedGroup.value = "";
        } else {
          selectedGroup.value = newValue;
        }
      }
    );

    // 监听 solution.groups 的变化，确保始终存在默认分组
    watch(
      () => props.solution?.groups,
      (groups) => {
        if (groups && (!Array.isArray(groups) || groups.length === 0)) {
          // 如果分组不存在或为空数组，自动创建默认分组
          console.log("检测到没有分组，自动创建默认分组");
          emit("add-group", {
            name: DEFAULT_GROUPS[0],
            silent: true, // 添加silent标记，表示这是自动创建的，不需要显示提示
          });
        }
      },
      { immediate: true }
    );

    // 方法 - 初始化 Logo 预览
    const initLogoPreview = async () => {
      try {
        if (props.solution && props.solution.logo) {
          const logoPath = await electronAPI.getResourceFilePath(
            props.solution.logo
          );
          if (logoPath) {
            logoInRequest.value = false;
            logoPreview.value = logoPath;
            return;
          }
        }
        logoInRequest.value = false;
        // logoPreview.value = defaultLogoPath;
      } catch (error) {
        console.error("获取方案 Logo 失败:", error);
        // logoPreview.value = defaultLogoPath;
        logoInRequest.value = false;
      }
    };

    const handleLogoUpload = async () => {};

    // 选择 Logo
    const showLogoUpload = async () => {
      // 如果已经在选择文件，直接返回
      if (isSelectingFile.value) {
        return;
      }

      try {
        isSelectingFile.value = true;
        // 调用 Electron API 打开文件选择对话框
        const result = await electronAPI.selectFiles({
          properties: ["openFile"],
          fileType: "image", // 指定文件类型为图片
          title: "选择方案 Logo 图片",
        });

        if (!result || result.length === 0) {
          return;
        }

        const filePath = result[0].path;
        console.log("🌈图片路径", filePath);

        const fileSizeMB = result[0].size / (1000 * 1000);
        console.log("🌈文件信息", result[0]);

        if (fileSizeMB > 20) {
          throw new Error(
            `图片大小不能超过20MB\n当前大小: ${fileSizeMB.toFixed(2)}MB`
          );
        }

        // 创建临时图片对象验证尺寸
        const img = new Image();
        img.src = `file://${filePath.replace(/\\/g, "/")}`;

        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = () => reject(new Error("图片加载失败"));
        });

        // 验证尺寸
        if (img.naturalWidth !== 322 || img.naturalHeight !== 126) {
          throw new Error(
            `图片尺寸必须为 322×126 像素\n当前尺寸: ${img.naturalWidth}×${img.naturalHeight}像素`
          );
        }

        // 保存方案 Logo
        const fileName = await electronAPI.saveSolutionLogo(filePath);

        if (fileName) {
          // 获取新 Logo 的完整路径
          const logoPath = await electronAPI.getResourceFilePath(fileName);

          // 更新预览
          logoPreview.value = logoPath || defaultLogoPath;

          // 更新方案对象，只包含必要的可序列化属性
          const updatedSolution = {
            UUID: props.solution.UUID,
            name: props.solution.name,
            description: props.solution.description,
            groups: props.solution.groups,
            list: props.solution.list,
            pcVersion: props.solution.pcVersion,
            port: props.solution.port,
            createdAt: props.solution.createdAt,
            updatedAt: Date.now(),
            logo: fileName,
          };

          // 确保所有属性都是可序列化的
          const serializedSolution = JSON.parse(
            JSON.stringify(updatedSolution)
          );
          emit("update:solution", serializedSolution);

          ElMessage.success("方案 Logo 已更新");
        } else {
          ElMessage.error("保存方案 Logo 失败");
        }
      } catch (error) {
        console.error("上传LOGO图片失败:", error);
        // 根据不同错误类型提供更友好的提示
        let message = error.message;
        if (message.includes("尺寸必须为")) {
          message = "LOGO图片尺寸不匹配\n" + message;
        } else if (message.includes("大小不能超过")) {
          message = "LOGO图片过大\n" + message;
        }
        ElMessage.error(error.message);
      } finally {
        isSelectingFile.value = false;
      }
    };

    // 监听 solution 变化
    watch(
      () => props.solution,
      () => {
        initLogoPreview();
      },
      { immediate: true }
    );

    // 新增：右下角设置按钮和菜单
    const showSettingsMenu = ref(false);
    const changeVirtualScene = () => {
      showSettingsMenu.value = false;
      openBgDialog();
    };

    const openBgDialog = () => {
      showSettingsMenu.value = false;
      showBgDialog.value = true;
      bgType.value = props.solution.background ? "custom" : "default";
      customBgFileName.value = props.solution.background || "";
    };

    // 方法 - 处理背景图确认
    const handleBgConfirm = async () => {
      try {
        if (bgType.value === "default") {
          // 调用后端API删除背景图
          const success = await electronAPI.deleteSolutionBackground();
          if (success) {
            bgImagePath.value = ""; // 清除背景图路径
            customBgFileName.value = "";
            showBgDialog.value = false;
          } else {
            throw new Error("删除背景图失败");
          }
        }

        // 更新方案对象，移除背景图
        const updatedSolution = {
          UUID: props.solution.UUID,
          name: props.solution.name,
          description: props.solution.description,
          groups: props.solution.groups,
          list: props.solution.list,
          pcVersion: props.solution.pcVersion,
          port: props.solution.port,
          createdAt: props.solution.createdAt,
          updatedAt: Date.now(),
          background: bgImagePath.value || "",
        };

        // 确保所有属性都是可序列化的
        const serializedSolution = JSON.parse(JSON.stringify(updatedSolution));

        console.log("🌈 方案数据", serializedSolution);
        emit("update:solution", serializedSolution);
        showBgDialog.value = false;
      } catch (error) {
        console.error("更新背景图失败:", error);
        alertService.alert({
          title: "操作失败",
          message: "更新背景图失败: " + (error.message || "未知错误"),
          type: "error",
        });
      }
    };

    const bgImagePath = ref("");

    const handleBgUpload = async () => {
      if (isSelectingFile.value) return;

      try {
        isSelectingFile.value = true;
        const result = await electronAPI.selectFiles({
          properties: ["openFile"],
          fileType: "image",
          title: "选择背景图片 (必须为 8192×4096 像素，小于50MB)",
        });

        if (!result || result.length === 0) return;

        const filePath = result[0].path;
        const fileName = filePath.split(/[/\\]/).pop();

        const fileSizeMB = result[0].size / (1000 * 1000);
        console.log("🌈文件信息", result[0]);

        if (fileSizeMB > 50) {
          throw new Error(
            `图片大小不能超过50MB\n当前大小: ${fileSizeMB.toFixed(2)}MB`
          );
        }

        // 创建临时图片对象验证尺寸
        const img = new Image();
        img.src = `file://${filePath.replace(/\\/g, "/")}`;

        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = () => reject(new Error("图片加载失败"));
        });

        // 验证尺寸
        if (img.naturalWidth !== 8192 || img.naturalHeight !== 4096) {
          throw new Error(
            `图片尺寸必须为 8192×4096 像素\n当前尺寸: ${img.naturalWidth}×${img.naturalHeight}像素`
          );
        }

        customBgFileName.value = fileName;
        bgImagePath.value = await electronAPI.saveSolutionBackground(filePath);
      } catch (error) {
        console.error("上传背景图片失败:", error);
        // 根据不同错误类型提供更友好的提示
        let message = error.message;
        if (message.includes("尺寸必须为")) {
          message = "背景图片尺寸不匹配\n" + message;
        } else if (message.includes("大小不能超过")) {
          message = "背景图片过大\n" + message;
        }
        ElMessage.error(error.message);
      } finally {
        isSelectingFile.value = false;
      }
    };

    // 版本信息
    const versionInfo = ref({
      version: "1.0.0",
      name: "PDM",
      productName: "Device Manager",
      description: "",
      copyright: `© ${new Date().getFullYear()} All rights reserved.`,
      company: "",
    });

    // 获取版本信息
    const fetchVersionInfo = async () => {
      try {
        const result = await electronAPI.getAppVersion();
        if (result) {
          versionInfo.value = result;
        }
      } catch (err) {
        console.error("获取版本信息失败:", err);
      }
    };

    // 在组件挂载时获取版本信息
    onMounted(async () => {
      await fetchVersionInfo();
    });

    // 右键菜单相关
    const contextMenuVisible = ref(false);
    const contextMenuStyle = ref({
      left: "0px",
      top: "0px",
    });
    const contextMenuData = ref(null);

    // 处理右键菜单
    const handleNodeContextMenu = (event, data) => {
      event.preventDefault();
      contextMenuData.value = data;
      // contextMenuStyle.value = {
      //   left: event.clientX + "px",
      //   top: event.clientY + 15 + "px",
      // };
      // 获取 el-tree 容器的左侧坐标
      const treeEl = document.querySelector(".group-tree");
      const left = treeEl ? treeEl.getBoundingClientRect().left : 0;
      contextMenuStyle.value = {
        left: left + "px",
        top: event.clientY + 15 + "px",
      };
      contextMenuVisible.value = true;
    };

    // 处理右键菜单编辑
    const handleContextMenuEdit = () => {
      if (contextMenuData.value) {
        startEditingGroup(contextMenuData.value);
      }
      contextMenuVisible.value = false;
    };

    // 处理右键菜单添加子分类
    const handleContextMenuAddSubcategory = () => {
      if (contextMenuData.value) {
        showAddSubcategoryDialog(contextMenuData.value);
      }
      contextMenuVisible.value = false;
    };

    // 处理右键菜单删除
    const handleContextMenuDelete = () => {
      if (contextMenuData.value) {
        deleteDialog.value = true;
        // confirmDeleteGroup(contextMenuData.value);
      }
      contextMenuVisible.value = false;
    };

    // 点击其他地方关闭右键菜单
    onMounted(() => {
      document.addEventListener("click", () => {
        contextMenuVisible.value = false;
      });
    });

    onUnmounted(() => {
      document.removeEventListener("click", () => {
        contextMenuVisible.value = false;
      });
    });

    // 响应式数据

    const logoDialog = ref(false);
    const deleteDialog = ref(false);
    const logoInRequest = ref(true);

    return {
      logoDialog,
      deleteDialog,

      // 状态
      selectedGroup,
      selectedResource,
      searchQuery,
      typeFilter,
      sortOption,
      expandedGroups,
      deleteResourcesWithGroup,
      hierarchicalGroups,
      mainTypeOptions,
      currentLocationTitle,
      filteredResources,
      groupResourceCounts,
      DEFAULT_GROUPS,

      // 分组编辑状态
      editingGroupId,
      editingGroupName,

      // 添加分组状态
      isAddingGroup,
      newGroupName,

      // 添加子分类状态
      isAddingSubcategory,
      addingSubcategoryParent,
      newSubcategoryName,

      // 基本方法
      selectGroup,
      toggleGroupExpand,
      toggleAllGroups,
      getResourcePreviewUrl,
      isImageType,
      isVideoType,
      getTypeLabel,
      getDisplayGroupName,

      // 分组操作方法
      showAddGroupDialog,
      showAddSubcategoryDialog,
      handleAddConfirm,
      confirmDeleteGroup,

      // 内联添加方法
      startAddingGroup,
      saveNewGroup,
      cancelAddingGroup,
      startAddingSubcategory,
      saveNewSubcategory,
      cancelAddingSubcategory,

      // 内联编辑方法
      startEditingGroup,
      handleEditConfirm,

      // 资源操作方法
      showAddResourceDialog,
      editResource,
      confirmDeleteResource,

      // 媒体预览相关
      showMediaPreview,
      mediaPreviewType,
      currentMedia,
      mediaUrl,
      previewMedia,
      playVideo,
      previewImage,
      closeMediaPreview,
      handleMediaError,

      // 新方法
      logoInRequest,
      logoPreview,
      showLogoUpload,

      // 新增：右下角设置按钮和菜单
      showSettingsMenu,
      changeVirtualScene,

      // 新增弹窗状态
      showBgDialog,
      bgType,
      customBgFileName,

      // 新增标志
      isSelectingFile,

      // 新增：自定义背景图方法
      openBgDialog,
      handleBgUpload,

      // 新增：自定义背景图方法
      handleBgConfirm,

      // Tree组件的配置
      defaultProps,
      treeData,
      defaultExpandedKeys,
      selectedNodeKey,
      handleNodeClick,
      editDialogVisible,
      addDialogVisible,
      editForm,
      addForm,
      isEditingSubcategory,

      // 版本信息
      versionInfo,

      // 右键菜单相关
      contextMenuVisible,
      contextMenuStyle,
      contextMenuData,

      // 处理右键菜单
      handleNodeContextMenu,
      handleContextMenuEdit,
      handleContextMenuAddSubcategory,
      handleContextMenuDelete,
    };
  },
};
</script>

<style scoped>
.resource-manager-outer {
  width: 70%;
  height: 65vh;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}
.resource-manager {
  height: 100%;
  border-radius: 16px !important;
  box-shadow: var(--shadow-sm) !important;
  overflow: hidden !important;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 1px;
  background-color: var(--color-box-bg);
}
/* 主布局 */
.resource-manager {
  display: grid;
  grid-template-columns: 15vw 1fr;
  gap: 1px;
  height: 100%;
}

.group-navigation {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 0 16px 30px;
  position: relative;
  min-width: 180px;
}

/* .solution-logo {
  width: 64px;
  height: 64px;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  position: relative;
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.2s ease;
  margin: 16px auto;
}

.solution-logo:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.solution-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.solution-logo i {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  font-size: 32px;
} */

.solution-logo {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  margin: 18px auto;

  width: 100%;
  aspect-ratio: 322/126;
}

.solution-logo:hover {
  transform: scale(1.05);
  border: 2px dashed #3593ff;
}
.solution-logo.empty {
  border: 2px dashed #d9e8f8;
}

.empty-logo {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  padding: 5px 0;
  width: 100%;
}
.empty-logo div {
  color: var(--color-menu-text);
  font-size: 10px;
  font-style: normal;
  font-weight: 340;
  line-height: normal;
}

.logo-img {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.logo-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  aspect-ratio: 322/126;
}

.logo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.solution-logo:hover .logo-overlay {
  opacity: 1;
}

.logo-overlay i {
  color: white;
  font-size: 24px;
}

[data-theme="dark"] .solution-logo {
  background-color: var(--color-background-dark);
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .solution-logo i {
  color: var(--color-text-secondary-dark);
}

.group-tree {
  padding: var(--spacing-xs);
  flex: 1;
  overflow: overlay;
  scrollbar-width: none;
}

.group-branch {
  margin-bottom: var(--spacing-xs);
}
.add-group-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-content: flex-start;
  text-align: left;
  margin: 0 5px 0;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;

  color: #6e6e6e;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.add-group-content:hover {
  background-color: var(--color-dialog-background);
}

.add-group-input {
  flex: 1;
  padding: var(--spacing-xs);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-size: 0.9em;
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.add-subcategory-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-md);
  margin-left: var(--spacing-md);
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  border: 1px dashed var(--color-border-dark);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.add-subcategory-item:hover {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  border-color: var(--color-primary);
}

.add-subcategory-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  justify-content: flex-start;
  text-align: left;
}

.add-subcategory-content i {
  color: var(--color-primary-dark);
  font-size: 1.1em;
}

.add-subcategory-input {
  flex: 1;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-size: 0.9em;
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: background-color 0.2s;
}

.group-item:hover {
  background-color: var(--color-hover);
}

.group-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  color: var(--color-primary-dark);
  font-weight: 600;
  box-shadow: 0 0 0 1px var(--color-primary);
}

.group-item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  overflow: hidden;
  justify-content: flex-start;
  text-align: left;
}

.group-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.group-icon i {
  font-size: 16px;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.group-icon i.active {
  color: var(--color-primary-dark);
}

.group-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  width: 180px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.group-count {
  font-size: 0.85em;
  color: var(--color-text-secondary);
  font-weight: normal;
}

.group-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s;
}

.group-item:hover .group-actions,
.group-item.active .group-actions {
  opacity: 1;
}

.expand-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 4px;
  transition: all 0.2s ease;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: 50%;
  z-index: 5;
}

.expand-toggle:hover {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  transform: scale(1.1);
}

.expand-toggle i {
  font-size: 10px;
  color: var(--color-primary-dark);
}

.icon-arrow-down {
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid var(--color-primary-dark);
  transition: transform 0.3s ease;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

.rotated {
  transform: rotate(-90deg);
}

.subcategory-list {
  margin-left: var(--spacing-md);
  margin-top: var(--spacing-xs);
  border-left: 1px solid var(--color-border-light);
}

.subcategory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  padding-left: var(--spacing-lg);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.subcategory-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: var(--spacing-md);
  height: 1px;
  background-color: var(--color-border-light);
}

.subcategory-item:hover {
  background-color: var(--color-hover);
}

.subcategory-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  color: var(--color-primary-dark);
  font-weight: 600;
  box-shadow: 0 0 0 1px var(--color-primary);
}

.subcategory-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
  overflow: hidden;
  justify-content: flex-start;
  text-align: left;
  padding-left: var(--spacing-sm);
}

.subcategory-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.subcategory-icon i {
  font-size: 14px;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.subcategory-icon i.active {
  color: var(--color-primary-dark);
}

.subcategory-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9em;
  text-align: left;
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.subcategory-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s;
}

.subcategory-item:hover .subcategory-actions,
.subcategory-item.active .subcategory-actions {
  opacity: 1;
}

.subcategory-divider {
  height: 1px;
  background-color: var(--color-border-light);
  margin: var(--spacing-sm) 0;
  margin-left: var(--spacing-md);
  opacity: 0.7;
}

.resource-view {
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 30px 20px;
  overflow-y: auto;
}

.resource-grid {
  padding: var(--spacing-md);
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: start;
  /* 移除 grid-auto-rows，确保卡片高度自适应 */
}

.resource-grid.empty {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: start;
}

/* 确保 ResourceCard 没有外部 margin，统一用 gap 控制间距 */
:deep(.resource-card),
:deep(.add-resource-card) {
  margin: 0 !important;
}

.add-resource-card {
  border-radius: var(--border-radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  cursor: pointer;
  transition:
    background-color 0.2s,
    border-color 0.2s;
  aspect-ratio: 1.72;
  gap: 4px;
  background-color: var(--color-box-bg);
}

.add-resource-card:hover {
  border: 2px dashed var(--color-primary);
}

.add-icon {
  font-size: 2em;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.action-panel {
  background-color: var(--color-background);
  border-left: 1px solid var(--color-border-light);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.panel-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.info-content {
  padding: var(--spacing-md);
}

.info-item {
  margin-bottom: var(--spacing-md);
}

.info-item label {
  display: block;
  font-size: 0.9em;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.panel-actions {
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
  border-top: 1px solid var(--color-border-light);
  margin-top: auto;
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-hover);
}

.btn-danger {
  background-color: var(--color-danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--color-danger-dark);
}

.resource-preview {
  padding: var(--spacing-md);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-background-dark);
  height: 200px;
  overflow: hidden;
}

.resource-preview img,
.resource-preview video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.app-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.app-preview i {
  font-size: 3em;
  margin-bottom: var(--spacing-sm);
}

.resource-info {
  padding: var(--spacing-md);
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tag {
  background-color: var(--color-background-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9em;
}

.btn-add-tag {
  background: none;
  border: 1px dashed var(--color-border-dark);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-xs);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-add-tag:hover {
  background-color: var(--color-hover);
  border-color: var(--color-primary);
}

.edit-input {
  width: 100%;
  padding: var(--spacing-xs);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-size: 0.9em;
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
  transition: all 0.2s ease;
}

.edit-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.3);
}

[data-theme="dark"] .resource-manager-layout {
  background-color: var(--color-border-dark);
}

[data-theme="dark"] .group-navigation,
[data-theme="dark"] .resource-view,
[data-theme="dark"] .action-panel {
  background-color: var(--color-background-dark);
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .nav-header {
  background-color: var(--color-box-bg);
  border-color: var(--color-border-dark);
  color: var(--color-text-primary-dark);
}

[data-theme="dark"] .btn-expand-all {
  background-color: rgba(var(--color-primary-rgb), 0.3);
  border-color: var(--color-primary);
  color: var(--color-primary-light);
}

[data-theme="dark"] .btn-expand-all:hover {
  background-color: rgba(var(--color-primary-rgb), 0.4);
  color: var(--color-primary-light);
  border-color: var(--color-primary-light);
}

[data-theme="dark"] .subcategory-list {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .all-resources {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .subcategory-item::before {
  background-color: var(--color-border-dark);
}

[data-theme="dark"] .subcategory-divider {
  background-color: var(--color-border-dark);
}

[data-theme="dark"] .nav-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  color: var(--color-primary-light);
  box-shadow: 0 0 0 1px var(--color-primary);
}

[data-theme="dark"] .group-item.active,
[data-theme="dark"] .subcategory-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  box-shadow: 0 0 0 1px var(--color-primary);
  color: var(--color-primary-light);
}

[data-theme="dark"] .icon-arrow-down {
  border-top-color: var(--color-primary-light);
}

[data-theme="dark"] .group-item.active .btn-icon,
[data-theme="dark"] .subcategory-item.active .btn-icon {
  color: var(--color-primary-light);
}

[data-theme="dark"] .group-item.active .btn-icon:hover,
[data-theme="dark"] .subcategory-item.active .btn-icon:hover {
  background-color: rgba(var(--color-primary-rgb), 0.2);
  color: var(--color-primary-lighter);
}

[data-theme="dark"] .view-header {
  border-color: var(--color-border-dark);
  background-color: var(--color-box-bg);
}

[data-theme="dark"] .panel-header {
  border-color: var(--color-border-dark);
}

[data-theme="dark"] .resource-count,
[data-theme="dark"] .group-count {
  color: var(--color-text-secondary-dark);
}

[data-theme="dark"] .search-box input,
[data-theme="dark"] .filter-select,
[data-theme="dark"] .sort-select {
  background-color: var(--color-background-dark);
  border-color: var(--color-border-dark);
  color: var(--color-text-primary-dark);
}

[data-theme="dark"] .filter-select:focus,
[data-theme="dark"] .sort-select:focus {
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

[data-theme="dark"] .filter-select:hover,
[data-theme="dark"] .sort-select:hover {
  border-color: var(--color-primary-light);
}

[data-theme="dark"] .add-resource-card {
  border-color: var(--color-primary);
  border-width: 2px;
  border-style: dashed;
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .add-resource-card:hover {
  background-color: var(--color-hover-dark);
  border-color: var(--color-primary-light);
}

[data-theme="dark"] .tag {
  background-color: var(--color-background-darker);
}

[data-theme="dark"] .btn-add-tag {
  border-color: var(--color-primary);
  border-width: 1px;
  border-style: dashed;
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--color-background-darker);
  border-color: var(--color-border-dark);
  color: var(--color-text-primary-dark);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--color-hover-dark);
}

[data-theme="dark"] .resource-preview {
  background-color: var(--color-background-darkest);
}

[data-theme="dark"] .edit-input,
[data-theme="dark"] .add-group-input,
[data-theme="dark"] .add-subcategory-input {
  background-color: var(--color-background-dark);
  color: var(--color-text-primary-dark);
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.15);
}

[data-theme="dark"] .edit-input:focus,
[data-theme="dark"] .add-group-input:focus,
[data-theme="dark"] .add-subcategory-input:focus {
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.25);
}

[data-theme="dark"] .add-subcategory-item {
  border-color: var(--color-border-dark);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

[data-theme="dark"] .add-subcategory-item:hover {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  border-color: var(--color-primary);
}

[data-theme="dark"] .add-subcategory-content i {
  color: var(--color-primary-light);
}

@media (max-width: 1200px) {
  .resource-manager {
    grid-template-columns: 280px 1fr;
  }
}

@media (max-width: 992px) {
  .resource-manager {
    grid-template-columns: 280px 1fr;
  }

  .search-box input {
    width: 150px;
  }
}

/* 新增：右下角设置按钮样式 */
.settings-btn {
  position: fixed;
  right: 32px;
  bottom: 32px;
  z-index: 100;
  cursor: pointer;
}
.settings-btn img {
  width: 54px;
  height: 54px;
}

.settings-menu {
  position: fixed;
  right: 32px;
  bottom: 100px;
  background-color: var(--color-box-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  padding: 8px 0;
  z-index: 101;
}
.settings-menu-item {
  padding: 10px 20px;
  color: var(--color-text-primary);
  cursor: pointer;
  background-color: var(--color-box-bg);
}
.settings-menu-item:hover {
  background: var(--color-hover);
}

/* 新增：自定义背景图样式 */
.custom-bg-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.25);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-bg-dialog {
  /* background-color: var(--color-dialog-background); */
  background-color: var(--color-box-bg);
  border-radius: 8px;
  min-width: 340px;
  width: 45vw;
  padding: 34px;
  position: relative;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
}
.custom-bg-dialog-title {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-menu-text);
  font-size: 24px;
  text-align: center;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
}
.custom-bg-dialog-content {
  font-size: 15px;
  color: #333;
  margin: 34px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.radio-option {
  font-size: 15px;
  margin-right: 12px;
  cursor: pointer;

  border-radius: 16px;
  width: 100%;
  padding: 24px 34px;
  background-color: var(--color-dialog-background);
  margin-bottom: 12px;

  color: var(--color-menu-text);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.radio-option input {
  cursor: pointer;
  margin-right: 30px;
  /* Make radio buttons appear square */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-primary);
  background-color: var(--color-dialog-background);
  border-radius: 0; /* Change from default circular to square */
}

/* Custom styling for checked state */
.radio-option input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  position: relative;
}

/* Add checkmark for checked state */
.radio-option input:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 0;
  width: 5px;
  height: 10px;
  border: solid var(--color-white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.custom-bg-upload {
  margin-top: 24px;
  border-top: 1px solid #bcbcbc;
  padding-top: 24px;
  display: flex;
  justify-content: space-between;
}
.res-name {
  display: inline-flex;
  align-items: center;
  width: 100px;
  color: var(--Interput, #0a84ff);

  flex: 1;
  font-size: 14px;
  font-style: normal;
  font-weight: 340;
  line-height: 22px;
  margin-right: 30px;
  overflow: hidden;
  white-space: nowrap;
  text-align: right; /* 文本右对齐 */
}
.res-name img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}
.upload-btn {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 16px;
  cursor: pointer;
  font-size: 14px;
  margin-left: auto;
}
.custom-bg-dialog-close {
  position: absolute;
  left: 20px;
  top: 20px;
  width: 28px;
  height: 28px;
  padding: 4px;
  cursor: pointer;
}

/* 新增：自定义背景图样式 */
.custom-bg-dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
}

.bottom-text {
  margin-top: 10px;
  color: #6e6e6e;
  font-size: 14px;
  font-style: normal;
  font-weight: 340;
  line-height: 22px; /* 157.143% */
}

.cancel-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: var(--color-primary);
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  width: 220px;
}

.confirm-btn:hover {
  background: var(--color-primary-dark);
}

.version-label {
  position: absolute;
  left: 16px;
  bottom: 18px;
  color: #6e6e6e;
  font-size: 8px;
  font-style: normal;
  font-weight: 340;
  line-height: normal;
  padding: 4px 8px;
  width: calc(100% - 32px);
  z-index: 1;
}

:deep(.resource-info h4) {
  font-size: 12px !important;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-menu-text);
  font-size: 12px;
  font-style: normal;
  font-weight: 480;
  line-height: normal;
}

.node-content {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
}

.node-label {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.node-label.is-current {
  color: var(--el-color-primary);
  font-weight: bold;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  flex-shrink: 0;
}

.custom-tree-node:hover .node-actions {
  opacity: 1;
}

.group-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 4px;
}

.add-group-button {
  margin-top: 16px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

:deep(.el-tree) {
  background-color: var(--color-box-bg);
  padding-bottom: 60px; /* 为底部按钮留出空间 */
}

:deep(.el-tree-node__content) {
  border-radius: 4px;
  padding: 20px 12px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #d9e8f8;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--color-box-bg);
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  min-width: 200px;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  z-index: 3000;
  padding: 12px;
  background-color: var(--color-box-bg);
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  color: ##eff6fe;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: var(--color-dialog-background);
  color: var(--color-primary);
}

.context-menu-item .el-icon {
  font-size: 16px;
}

.upload-logo {
  display: flex;
  padding: 30px 0;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background-color: #d9e8f8 !important;
}

:deep(.el-dialog) {
  position: absolute;
  top: 30%;
  left: 50%;
  width: 35vw;
  transform: translate(-50%, -50%);
  background-color: var(--color-dialog-background);
  padding: 34px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  border-radius: 16px;
}

:deep(.el-dialog__title) {
  color: var(--color-menu-text);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px; /* 75% */
}

:deep(.logo-dialog.el-dialog) {
  width: 25vw;
  background-color: #eff6fe;
}
:deep(.delete-dialog.el-dialog) {
  width: 20vw;
}

.upload-logo img {
  width: 25px;
  height: 25px;
}

.logo-tip {
  margin-top: 15px;
  color: var(--color-menu-text);
  text-align: center;
  font-size: 10px;
  font-style: normal;
  font-weight: 250;
  line-height: 30px; /* 300% */
}

.delete-tip {
  margin-top: 12px;
  color: var(--color-menu-text);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 340;
  line-height: 26px; /* 185.714% */
}
</style>
